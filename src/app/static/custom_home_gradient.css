/* Custom CSS for Superset Home Page Gradient Background */

/* Target the main content area of the home page */
.main-content,
.dashboard-content,
.ant-layout-content,
[data-test="welcome"] {
    background: linear-gradient(rgba(255, 255, 255, 0) 77.08%, rgb(255, 255, 255) 100%), 
                linear-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%) !important;
    min-height: 100vh;
}

/* Specifically target the welcome/home page container */
.welcome-container,
.welcome-content,
.ant-layout-content > div:first-child {
    background: linear-gradient(rgba(255, 255, 255, 0) 77.08%, rgb(255, 255, 255) 100%), 
                linear-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%) !important;
}

/* Target the body for home page specifically */
body[data-page="home"] .main-content,
body[data-page="welcome"] .main-content {
    background: linear-gradient(rgba(255, 255, 255, 0) 77.08%, rgb(255, 255, 255) 100%), 
                linear-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%) !important;
}

/* Alternative targeting for different Superset versions */
.superset-legacy-chart-header,
.slice_container,
.dashboard-component-tabs,
.grid-container {
    background: transparent !important;
}

/* Ensure cards and panels maintain their background on other pages */
.ant-card,
.panel,
.dashboard-component {
    background: #f5f5f9 !important;
}

/* Override for home page cards to be transparent/inherit gradient */
body[data-page="home"] .ant-card,
body[data-page="welcome"] .ant-card,
[data-test="welcome"] .ant-card {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
